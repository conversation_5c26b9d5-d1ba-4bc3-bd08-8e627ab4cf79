<template>
  <div class="min-h-screen p-4 bg-gradient-to-br sm:p-6">
    <div class="max-w-6xl mx-auto">
      <!-- 标题区域 -->
      <div class="mb-6 text-center sm:mb-8">
        <h1 class="mb-2 text-xl font-bold text-gray-800 sm:text-2xl">
          Estimate your academic ability
        </h1>
        <div class="text-xs text-gray-600 sm:text-sm">
          Enter your X account to see which mutual followers have the same academic ability as you.
        </div>
      </div>

      <!-- 用户名输入框 -->
      <div class="flex justify-center mb-6 sm:mb-8">
        <div class="relative w-full ">
          <UInput
                  v-model="currentUsername"
                  size="lg"
                  class="w-full"
                  :ui="{
                    base: 'relative block disabled:cursor-not-allowed disabled:opacity-75 focus:outline-none h-[56px] sm:h-[64px] flex justify-between items-center bg-white rounded-[80px] border-[4px] border-[#F2F2F259] shadow-[0px_4px_24px_0px_#1226420D] px-4 sm:px-6 py-3',
                    rounded: 'rounded-[80px]',
                    placeholder: 'placeholder-gray-400 dark:placeholder-gray-500',
                    size: {
                      lg: 'text-sm sm:text-base'
                    },
                    color: {
                      white: {
                        outline: ''
                      }
                    }
                  }">
            <template #prefix>
              <p class="text-sm sm:text-base">@</p>
            </template>
            <template #trailing>
              <div class="flex space-x-1">
                <UButton
                         @click="handleSubmit"
                         :disabled="!username.trim()"
                         variant="ghost"
                         class=""
                         :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                  <img src="~/assets/images/ai.png" alt="AI Icon" class="w-6 h-6 sm:w-8 sm:h-8"></img>
                </UButton>
                <div class="hidden sm:block"></div>
                <UButton
                         variant="ghost"
                         class="hidden sm:inline-flex"
                         :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                  <img src="~/assets/images/x.png" alt="X Icon" class="w-6 h-6 sm:w-8 sm:h-8"></img>
                </UButton>

                <UButton
                         variant="ghost"
                         class="hidden sm:inline-flex"
                         :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                  <img src="~/assets/images/tg.png" alt="Telegram Icon" class="w-6 h-6 sm:w-8 sm:h-8"></img>
                </UButton>

                <UButton
                         variant="ghost"
                         class="hidden sm:inline-flex"
                         :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                  <img src="~/assets/images/github.png" alt="GitHub Icon" class="w-6 h-6 sm:w-8 sm:h-8"></img>
                </UButton>
              </div>
            </template>
          </UInput>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="space-y-4 sm:space-y-6 lg:grid lg:grid-cols-2 lg:gap-6 lg:space-y-0">
        <!-- 雷达图区域 -->
        <div
             class="shadow-lg bg-[#b6d9f7] border border-solid border-[#fff] border-width-2 backdrop-blur-sm rounded-3xl p-4 sm:p-6">
          <!-- 动态雷达图组件 -->
          <RadarChart :username="currentUsername" :image="image" :data="radarData" :labels="radarLabels" :max-value="10" />

          <!-- 底部说明文字 -->
          <div
               class="mt-4 p-4 sm:p-6 leading-5 sm:leading-6 w-full text-xs text-left text-red-500 border-t border-solid border-[#fff] border-width-2 font-bold">
            Knowledge spans multiple fields, capable of solving various problems; logical reasoning is strong with clear
            context; expression is precise and flexible, quick to learn and adapt, able to meet different scenarios.
          </div>
        </div>

        <!-- 右侧面板 -->
        <div class="space-y-4">
          <!-- 综合分数卡片 -->
          <div
               class="p-4 sm:p-6 shadow-lg bg-[#b6d9f7] border border-solid border-[#fff] border-width-2 backdrop-blur-sm rounded-3xl">
            <div
                 class="flex flex-col sm:flex-row sm:items-center sm:justify-between pb-4 mb-4 sm:mb-6 border-b border-[#008CFF26] space-y-2 sm:space-y-0">
              <div>
                <h3 class="text-base font-semibold text-gray-800 sm:text-lg">Comprehensive score</h3>
              </div>
              <div class="text-xs font-medium text-red-400">Light up a Crested Ibis +2 points</div>
            </div>

            <div
                 class="flex flex-col mb-4 space-y-4 sm:flex-row sm:items-center sm:justify-between sm:mb-6 sm:space-y-0">
              <!-- 分数显示 - 红色胶囊形状 -->
              <div class="px-6 py-3 text-white rounded-xl shadow-lg bg-[#FF3314] w-full sm:w-56 text-center">
                <span class="text-2xl font-bold sm:text-3xl">81</span>
                <span class="ml-1 text-sm opacity-80">/6</span>
              </div>

              <!-- 徽章组 -->
              <div
                   class="flex justify-center sm:justify-start space-x-1 sm:space-x-2 border border-solid bg-transparent border-[#fff] rounded-xl px-3 sm:px-6 py-3">
                <div class="flex items-center justify-center w-8 h-8 rounded-full sm:w-10 sm:h-10">
                  <img src="~/assets/images/logo.png" alt="Ibis Icon" class="w-8 h-8 sm:w-10 sm:h-10">
                </div>
                <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10">
                  <img src="~/assets/images/logo.png" alt="Ibis Icon" class="w-8 h-8 sm:w-10 sm:h-10">
                </div>
                <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10">
                  <img src="~/assets/images/logo.png" alt="Ibis Icon" class="w-8 h-8 sm:w-10 sm:h-10">
                </div>
                <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10">
                  <img src="~/assets/images/logo.png" alt="Ibis Icon" class="w-8 h-8 sm:w-10 sm:h-10">
                </div>
                <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10">
                  <img src="~/assets/images/logo.png" alt="Ibis Icon" class="w-8 h-8 sm:w-10 sm:h-10">
                </div>
              </div>
            </div>

            <div class="mt-4">
              <div class="mb-2 text-sm font-medium text-gray-800">Save your diagram</div>
              <div class="mb-4 text-xs text-gray-500">
                Save your academic ability assessment results to your device for future reference.
              </div>
              <UButton block size="lg"
                       class="py-3 font-medium text-white border-0 shadow-lg bg-gradient-to-r from-blue-400 to-blue-500 hover:from-blue-500 hover:to-blue-600 rounded-2xl"
                       :ui="{
                        base: 'cursor-pointer focus:outline-none focus-visible:outline-0 disabled:cursor-not-allowed disabled:opacity-75 flex-shrink-0',
                        font: 'font-medium',
                        rounded: 'rounded-2xl',
                        size: {
                          lg: 'text-sm sm:text-base px-4 sm:px-6 py-3'
                        }
                      }">
                <svg class="w-4 h-4 mr-2 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export Avatar
              </UButton>
            </div>
          </div>

          <!-- 分享卡片 -->
          <div
               class="p-4 sm:p-6 shadow-lg bg-[#b6d9f7] border border-solid border-[#fff] border-width-2 backdrop-blur-sm rounded-3xl">
            <div class="mb-4 text-sm font-medium text-gray-800">Share on Twitter</div>
            <UButton block size="lg"
                     class="py-3 font-medium text-white border-0 shadow-lg bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-2xl"
                     :ui="{
                      base: 'cursor-pointer focus:outline-none focus-visible:outline-0 disabled:cursor-not-allowed disabled:opacity-75 flex-shrink-0',
                      font: 'font-medium',
                      rounded: 'rounded-2xl',
                      size: {
                        lg: 'text-sm sm:text-base px-4 sm:px-6 py-3'
                      }
                    }">
              <svg class="w-4 h-4 mr-2 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              Click to share
            </UButton>
          </div>

          <!-- 移动端底部导航 -->
          <div class="flex justify-center mb-10 space-x-4 sm:hidden">
            <UButton variant="ghost" class="p-3"
                     :ui="{ base: 'cursor-pointer hover:bg-white/20 focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
              <img src="~/assets/images/x.png" alt="X Icon" class="w-8 h-8"></img>
            </UButton>
            <UButton variant="ghost" class="p-3"
                     :ui="{ base: 'cursor-pointer hover:bg-white/20 focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
              <img src="~/assets/images/tg.png" alt="Telegram Icon" class="w-8 h-8"></img>
            </UButton>
            <UButton variant="ghost" class="p-3"
                     :ui="{ base: 'cursor-pointer hover:bg-white/20 focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
              <img src="~/assets/images/github.png" alt="GitHub Icon" class="w-8 h-8"></img>
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const route = useRoute()
const currentUsername = ref(route.query.username || 'alphaHZH')
const username = ref(currentUsername.value)
const image = decodeURIComponent(route.query.image || 'https://example.com/default-avatar.png')

console.log(image);

// 雷达图数据配置
const radarLabels = ref(['SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI'])
const radarData = ref([6, 6, 6, 9.5, 8.5])

const updateRadarData = async () => {
  const { data } = await useFetch('/api/evaluateAcademicLevel', {
    query: {
      username: currentUsername.value
    }
  })

  if (data.value?.evaluation) {
    // 更新雷达图数据
    radarData.value = data.value.evaluation
    console.log('获取到的数据:', data.value)
  } else {
    console.error('获取数据失败')
  }
}


// 处理提交
const handleSubmit = () => {
  if (username.value.trim()) {
    currentUsername.value = username.value
    updateRadarData()
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  updateRadarData()
})
</script>
